import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_response.dart';
import '../models/auth_response.dart';
import '../models/user.dart';
import '../models/chat_room.dart';
import '../models/message.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8080/api';
  
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('access_token');
  }

  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', token);
  }

  static Future<void> saveRefreshToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('refresh_token', token);
  }

  static Future<void> clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('access_token');
    await prefs.remove('refresh_token');
  }

  static Future<Map<String, String>> getHeaders() async {
    final token = await getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Auth endpoints
  static Future<ApiResponse<AuthResponse>> login(String username, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'username': username,
        'password': password,
      }),
    );

    final jsonData = jsonDecode(response.body);
    return ApiResponse.fromJson(
      jsonData,
      (json) => AuthResponse.fromJson(json as Map<String, dynamic>),
    );
  }

  static Future<ApiResponse<AuthResponse>> register(
    String username,
    String email,
    String password,
    String confirmPassword,
    String? nickname,
  ) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'username': username,
        'email': email,
        'password': password,
        'confirmPassword': confirmPassword,
        'nickname': nickname,
      }),
    );

    final jsonData = jsonDecode(response.body);
    return ApiResponse.fromJson(
      jsonData,
      (json) => AuthResponse.fromJson(json as Map<String, dynamic>),
    );
  }

  static Future<ApiResponse<String>> logout() async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/logout'),
      headers: await getHeaders(),
    );

    final jsonData = jsonDecode(response.body);
    return ApiResponse.fromJson(jsonData, (json) => json as String);
  }

  // Chat room endpoints
  static Future<ApiResponse<List<ChatRoom>>> getPublicChatRooms() async {
    final response = await http.get(
      Uri.parse('$baseUrl/chatrooms/public'),
      headers: await getHeaders(),
    );

    final jsonData = jsonDecode(response.body);
    return ApiResponse.fromJson(
      jsonData,
      (json) => (json as List).map((item) => ChatRoom.fromJson(item)).toList(),
    );
  }

  static Future<ApiResponse<List<ChatRoom>>> getUserChatRooms() async {
    final response = await http.get(
      Uri.parse('$baseUrl/chatrooms/my'),
      headers: await getHeaders(),
    );

    final jsonData = jsonDecode(response.body);
    return ApiResponse.fromJson(
      jsonData,
      (json) => (json as List).map((item) => ChatRoom.fromJson(item)).toList(),
    );
  }

  static Future<ApiResponse<dynamic>> joinChatRoom(int chatRoomId) async {
    final response = await http.post(
      Uri.parse('$baseUrl/chatrooms/$chatRoomId/join'),
      headers: await getHeaders(),
    );

    final jsonData = jsonDecode(response.body);
    return ApiResponse.fromJson(jsonData, (json) => json);
  }

  // Message endpoints
  static Future<ApiResponse<List<Message>>> getRecentMessages(int chatRoomId, {int limit = 50}) async {
    final response = await http.get(
      Uri.parse('$baseUrl/messages/chatroom/$chatRoomId/recent?limit=$limit'),
      headers: await getHeaders(),
    );

    final jsonData = jsonDecode(response.body);
    return ApiResponse.fromJson(
      jsonData,
      (json) => (json as List).map((item) => Message.fromJson(item)).toList(),
    );
  }

  static Future<ApiResponse<Message>> sendMessage(int chatRoomId, String content) async {
    final response = await http.post(
      Uri.parse('$baseUrl/messages'),
      headers: await getHeaders(),
      body: jsonEncode({
        'chatRoomId': chatRoomId,
        'content': content,
        'messageType': 'TEXT',
      }),
    );

    final jsonData = jsonDecode(response.body);
    return ApiResponse.fromJson(
      jsonData,
      (json) => Message.fromJson(json as Map<String, dynamic>),
    );
  }
}
