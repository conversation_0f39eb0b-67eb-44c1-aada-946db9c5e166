package com.chatroom.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chatroom.dto.ChatRoomCreateRequest;
import com.chatroom.entity.ChatRoom;
import com.chatroom.entity.UserChatRoom;

import java.util.List;

public interface ChatRoomService extends IService<ChatRoom> {

    /**
     * 创建聊天室
     */
    ChatRoom createChatRoom(ChatRoomCreateRequest request, Long creatorId);

    /**
     * 加入聊天室
     */
    UserChatRoom joinChatRoom(Long chatRoomId, Long userId);

    /**
     * 离开聊天室
     */
    void leaveChatRoom(Long chatRoomId, Long userId);

    /**
     * 获取公共聊天室列表
     */
    List<ChatRoom> getPublicChatRooms();

    /**
     * 获取用户加入的聊天室列表
     */
    List<ChatRoom> getUserChatRooms(Long userId);

    /**
     * 获取聊天室成员列表
     */
    List<UserChatRoom> getChatRoomMembers(Long chatRoomId);

    /**
     * 获取聊天室在线成员列表
     */
    List<UserChatRoom> getOnlineMembers(Long chatRoomId);

    /**
     * 检查用户是否在聊天室中
     */
    boolean isUserInChatRoom(Long userId, Long chatRoomId);

    /**
     * 获取聊天室成员数量
     */
    int getMemberCount(Long chatRoomId);

    /**
     * 更新聊天室信息
     */
    ChatRoom updateChatRoom(Long chatRoomId, ChatRoomCreateRequest request, Long userId);

    /**
     * 删除聊天室
     */
    void deleteChatRoom(Long chatRoomId, Long userId);
}
