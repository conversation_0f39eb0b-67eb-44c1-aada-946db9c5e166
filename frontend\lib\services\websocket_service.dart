import 'dart:convert';
import 'dart:async';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../models/message.dart';

class WebSocketService {
  static const String wsUrl = 'ws://localhost:8080/ws/chat';
  
  WebSocketChannel? _channel;
  StreamController<Map<String, dynamic>>? _messageController;
  
  Stream<Map<String, dynamic>>? get messageStream => _messageController?.stream;
  
  bool get isConnected => _channel != null;

  Future<void> connect(String token, int chatRoomId) async {
    try {
      final uri = Uri.parse('$wsUrl?token=$token&chatRoomId=$chatRoomId');
      _channel = WebSocketChannel.connect(uri);
      
      _messageController = StreamController<Map<String, dynamic>>.broadcast();
      
      _channel!.stream.listen(
        (data) {
          try {
            final message = jsonDecode(data);
            _messageController?.add(message);
          } catch (e) {
            print('Error parsing WebSocket message: $e');
          }
        },
        onError: (error) {
          print('WebSocket error: $error');
          _messageController?.addError(error);
        },
        onDone: () {
          print('WebSocket connection closed');
          _messageController?.close();
        },
      );
      
      // Send ping to keep connection alive
      _startPingTimer();
      
    } catch (e) {
      print('Error connecting to WebSocket: $e');
      throw e;
    }
  }

  void _startPingTimer() {
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_channel != null) {
        sendMessage({
          'type': 'PING',
        });
      } else {
        timer.cancel();
      }
    });
  }

  void sendMessage(Map<String, dynamic> message) {
    if (_channel != null) {
      _channel!.sink.add(jsonEncode(message));
    }
  }

  void sendTyping(bool isTyping) {
    sendMessage({
      'type': 'TYPING',
      'data': isTyping,
    });
  }

  void disconnect() {
    _channel?.sink.close();
    _channel = null;
    _messageController?.close();
    _messageController = null;
  }
}
