-- 创建数据库和用户的完整脚本
-- 请根据您的MariaDB配置修改用户名和密码

-- 创建数据库
CREATE DATABASE IF NOT EXISTS chatroom_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选，如果使用root用户可以跳过）
-- CREATE USER IF NOT EXISTS 'chatroom_user'@'localhost' IDENTIFIED BY 'chatroom_password';
-- GRANT ALL PRIVILEGES ON chatroom_db.* TO 'chatroom_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 使用数据库
USE chatroom_db;

-- 执行表结构创建
SOURCE backend/src/main/resources/db/schema.sql;
