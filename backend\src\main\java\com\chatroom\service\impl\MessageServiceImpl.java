package com.chatroom.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chatroom.dto.MessageSendRequest;
import com.chatroom.entity.Message;
import com.chatroom.mapper.MessageMapper;
import com.chatroom.mapper.UserChatRoomMapper;
import com.chatroom.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements MessageService {

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private UserChatRoomMapper userChatRoomMapper;

    @Override
    @Transactional
    public Message sendMessage(MessageSendRequest request, Long senderId) {
        // 检查用户是否在聊天室中
        if (userChatRoomMapper.findByUserIdAndChatRoomId(senderId, request.getChatRoomId()) == null) {
            throw new RuntimeException("您不在该聊天室中");
        }

        Message message = new Message();
        message.setChatRoomId(request.getChatRoomId());
        message.setSenderId(senderId);
        message.setContent(request.getContent());
        message.setMessageType(request.getMessageType());
        message.setReplyToMessageId(request.getReplyToMessageId());

        save(message);
        return message;
    }

    @Override
    public IPage<Message> getChatRoomMessages(Long chatRoomId, int page, int size) {
        Page<Message> pageParam = new Page<>(page, size);
        return messageMapper.findMessagesByChatRoomId(pageParam, chatRoomId);
    }

    @Override
    public List<Message> getRecentMessages(Long chatRoomId, int limit) {
        return messageMapper.findRecentMessages(chatRoomId, limit);
    }

    @Override
    public List<Message> getNewMessages(Long chatRoomId, Long lastMessageId) {
        return messageMapper.findNewMessages(chatRoomId, lastMessageId);
    }

    @Override
    @Transactional
    public void deleteMessage(Long messageId, Long userId) {
        Message message = getById(messageId);
        if (message == null) {
            throw new RuntimeException("消息不存在");
        }

        if (!message.getSenderId().equals(userId)) {
            throw new RuntimeException("只能删除自己的消息");
        }

        removeById(messageId);
    }

    @Override
    @Transactional
    public Message editMessage(Long messageId, String newContent, Long userId) {
        Message message = getById(messageId);
        if (message == null) {
            throw new RuntimeException("消息不存在");
        }

        if (!message.getSenderId().equals(userId)) {
            throw new RuntimeException("只能编辑自己的消息");
        }

        message.setContent(newContent);
        message.setIsEdited(true);
        message.setEditedAt(LocalDateTime.now());

        updateById(message);
        return message;
    }

    @Override
    public int getMessageCount(Long chatRoomId) {
        return messageMapper.countMessagesByChatRoomId(chatRoomId);
    }
}
