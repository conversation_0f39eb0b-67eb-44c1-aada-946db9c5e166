import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/auth_response.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _error;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> checkAuthStatus() async {
    _setLoading(true);
    try {
      final token = await ApiService.getToken();
      if (token != null) {
        // TODO: Validate token with server
        // For now, assume token is valid if it exists
        final prefs = await SharedPreferences.getInstance();
        final userJson = prefs.getString('user');
        if (userJson != null) {
          // TODO: Parse user from stored data
        }
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await ApiService.login(username, password);
      
      if (response.success && response.data != null) {
        final authResponse = response.data!;
        _user = authResponse.user;
        
        // Save tokens
        await ApiService.saveToken(authResponse.accessToken);
        await ApiService.saveRefreshToken(authResponse.refreshToken);
        
        // Save user data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user', authResponse.user.toJson().toString());
        
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> register(
    String username,
    String email,
    String password,
    String confirmPassword,
    String? nickname,
  ) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await ApiService.register(
        username,
        email,
        password,
        confirmPassword,
        nickname,
      );
      
      if (response.success && response.data != null) {
        final authResponse = response.data!;
        _user = authResponse.user;
        
        // Save tokens
        await ApiService.saveToken(authResponse.accessToken);
        await ApiService.saveRefreshToken(authResponse.refreshToken);
        
        // Save user data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user', authResponse.user.toJson().toString());
        
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await ApiService.logout();
    } catch (e) {
      // Continue with logout even if API call fails
      print('Logout API error: $e');
    }
    
    // Clear local data
    await ApiService.clearTokens();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user');
    
    _user = null;
    _setError(null);
    notifyListeners();
    _setLoading(false);
  }

  void clearError() {
    _setError(null);
  }
}
