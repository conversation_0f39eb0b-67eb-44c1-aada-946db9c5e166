server:
  port: 8080

spring:
  application:
    name: chatroom-backend
  
  datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    url: ***********************************************************************************************************************
    username: root
    password: a228702862.
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto

# JWT Configuration
jwt:
  secret: mySecretKey123456789012345678901234567890
  expiration: 86400000 # 24 hours in milliseconds
  refresh-expiration: 604800000 # 7 days in milliseconds

# CORS Configuration
cors:
  allowed-origins: "*"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  allowed-headers: "*"
  allow-credentials: true

logging:
  level:
    com.chatroom: DEBUG
    org.springframework.security: DEBUG
