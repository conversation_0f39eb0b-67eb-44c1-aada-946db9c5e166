import 'package:flutter/foundation.dart';
import '../models/chat_room.dart';
import '../models/message.dart';
import '../services/api_service.dart';
import '../services/websocket_service.dart';

class ChatProvider with ChangeNotifier {
  List<ChatRoom> _publicChatRooms = [];
  List<ChatRoom> _userChatRooms = [];
  List<Message> _messages = [];
  ChatRoom? _currentChatRoom;
  bool _isLoading = false;
  String? _error;
  
  final WebSocketService _webSocketService = WebSocketService();
  
  List<ChatRoom> get publicChatRooms => _publicChatRooms;
  List<ChatRoom> get userChatRooms => _userChatRooms;
  List<Message> get messages => _messages;
  ChatRoom? get currentChatRoom => _currentChatRoom;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isConnected => _webSocketService.isConnected;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> loadPublicChatRooms() async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await ApiService.getPublicChatRooms();
      
      if (response.success && response.data != null) {
        _publicChatRooms = response.data!;
        notifyListeners();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadUserChatRooms() async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await ApiService.getUserChatRooms();
      
      if (response.success && response.data != null) {
        _userChatRooms = response.data!;
        notifyListeners();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> joinChatRoom(ChatRoom chatRoom) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await ApiService.joinChatRoom(chatRoom.id);
      
      if (response.success) {
        // Add to user chat rooms if not already there
        if (!_userChatRooms.any((room) => room.id == chatRoom.id)) {
          _userChatRooms.add(chatRoom);
          notifyListeners();
        }
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> enterChatRoom(ChatRoom chatRoom, String token) async {
    _currentChatRoom = chatRoom;
    _messages.clear();
    notifyListeners();
    
    // Load recent messages
    await loadRecentMessages(chatRoom.id);
    
    // Connect to WebSocket
    try {
      await _webSocketService.connect(token, chatRoom.id);
      
      // Listen to WebSocket messages
      _webSocketService.messageStream?.listen((data) {
        _handleWebSocketMessage(data);
      });
      
    } catch (e) {
      _setError('Failed to connect to chat: $e');
    }
  }

  void _handleWebSocketMessage(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    
    switch (type) {
      case 'MESSAGE':
        final message = Message.fromJson(data);
        _messages.add(message);
        notifyListeners();
        break;
      case 'USER_JOIN':
        // Handle user join event
        break;
      case 'USER_LEAVE':
        // Handle user leave event
        break;
      case 'TYPING':
        // Handle typing indicator
        break;
    }
  }

  Future<void> loadRecentMessages(int chatRoomId) async {
    try {
      final response = await ApiService.getRecentMessages(chatRoomId);
      
      if (response.success && response.data != null) {
        _messages = response.data!.reversed.toList(); // Reverse to show oldest first
        notifyListeners();
      }
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<bool> sendMessage(String content) async {
    if (_currentChatRoom == null) return false;
    
    try {
      final response = await ApiService.sendMessage(_currentChatRoom!.id, content);
      
      if (response.success && response.data != null) {
        // Message will be added via WebSocket
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  void sendTyping(bool isTyping) {
    _webSocketService.sendTyping(isTyping);
  }

  void leaveChatRoom() {
    _webSocketService.disconnect();
    _currentChatRoom = null;
    _messages.clear();
    notifyListeners();
  }

  void clearError() {
    _setError(null);
  }

  @override
  void dispose() {
    _webSocketService.disconnect();
    super.dispose();
  }
}
