package com.chatroom.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public class MessageSendRequest {
    
    @NotNull(message = "聊天室ID不能为空")
    private Long chatRoomId;
    
    @NotBlank(message = "消息内容不能为空")
    private String content;
    
    private String messageType = "TEXT";
    
    private Long replyToMessageId;

    // Constructors
    public MessageSendRequest() {}

    public MessageSendRequest(Long chatRoomId, String content) {
        this.chatRoomId = chatRoomId;
        this.content = content;
    }

    // Getters and Setters
    public Long getChatRoomId() {
        return chatRoomId;
    }

    public void setChatRoomId(Long chatRoomId) {
        this.chatRoomId = chatRoomId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Long getReplyToMessageId() {
        return replyToMessageId;
    }

    public void setReplyToMessageId(Long replyToMessageId) {
        this.replyToMessageId = replyToMessageId;
    }
}
