# 公共聊天室项目

一个基于SpringBoot + MyBatis Plus + MariaDB + JWT + Flutter的实时聊天室应用。

## 技术栈

### 后端
- **SpringBoot 3.2.0** - 主框架
- **MyBatis Plus 3.5.4** - ORM框架
- **MariaDB** - 数据库
- **JWT** - 身份认证
- **WebSocket** - 实时通信
- **Spring Security** - 安全框架

### 前端
- **Flutter** - 跨平台移动应用开发
- **Provider** - 状态管理
- **WebSocket** - 实时通信
- **HTTP** - API调用

## 功能特性

- ✅ 用户注册/登录
- ✅ JWT身份认证
- ✅ 公共聊天室列表
- ✅ 加入/离开聊天室
- ✅ 实时消息发送/接收
- ✅ WebSocket实时通信
- ✅ 在线用户状态管理
- ✅ 消息历史记录
- ✅ 响应式UI设计

## 项目结构

```
newTime/
├── backend/                 # SpringBoot后端
│   ├── src/main/java/
│   │   └── com/chatroom/
│   │       ├── config/      # 配置类
│   │       ├── controller/  # 控制器
│   │       ├── dto/         # 数据传输对象
│   │       ├── entity/      # 实体类
│   │       ├── filter/      # 过滤器
│   │       ├── handler/     # WebSocket处理器
│   │       ├── mapper/      # MyBatis映射器
│   │       ├── service/     # 服务层
│   │       └── util/        # 工具类
│   └── src/main/resources/
│       ├── application.yml  # 配置文件
│       └── db/schema.sql    # 数据库脚本
├── frontend/                # Flutter前端
│   ├── lib/
│   │   ├── models/          # 数据模型
│   │   ├── providers/       # 状态管理
│   │   ├── screens/         # 页面
│   │   ├── services/        # 服务层
│   │   ├── utils/           # 工具类
│   │   └── widgets/         # 自定义组件
│   └── pubspec.yaml         # 依赖配置
└── README.md
```

## 快速开始

### 环境要求

- Java 17+
- Maven 3.6+
- MariaDB 10.5+
- Flutter 3.0+
- Dart 3.0+

### 后端启动

1. **安装MariaDB并创建数据库**
   ```sql
   CREATE DATABASE chatroom_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **配置数据库连接**
   
   编辑 `backend/src/main/resources/application.yml`：
   ```yaml
   spring:
     datasource:
       url: *****************************************
       username: your_username
       password: your_password
   ```

3. **运行数据库脚本**
   ```bash
   mysql -u your_username -p chatroom_db < backend/src/main/resources/db/schema.sql
   ```

4. **启动后端服务**
   ```bash
   cd backend
   mvn spring-boot:run
   ```

   服务将在 http://localhost:8080 启动

### 前端启动

1. **安装Flutter依赖**
   ```bash
   cd frontend
   flutter pub get
   ```

2. **生成代码**
   ```bash
   flutter packages pub run build_runner build
   ```

3. **运行Flutter应用**
   ```bash
   flutter run
   ```

## API文档

### 认证接口

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新token
- `GET /api/auth/me` - 获取当前用户信息

### 聊天室接口

- `GET /api/chatrooms/public` - 获取公共聊天室列表
- `GET /api/chatrooms/my` - 获取用户加入的聊天室
- `POST /api/chatrooms` - 创建聊天室
- `POST /api/chatrooms/{id}/join` - 加入聊天室
- `POST /api/chatrooms/{id}/leave` - 离开聊天室
- `GET /api/chatrooms/{id}/members` - 获取聊天室成员

### 消息接口

- `POST /api/messages` - 发送消息
- `GET /api/messages/chatroom/{id}/recent` - 获取最近消息
- `GET /api/messages/chatroom/{id}` - 分页获取消息
- `DELETE /api/messages/{id}` - 删除消息
- `PUT /api/messages/{id}` - 编辑消息

### WebSocket

- 连接地址: `ws://localhost:8080/ws/chat?token={jwt_token}&chatRoomId={room_id}`
- 支持实时消息推送、用户状态更新、输入状态等

## 数据库设计

### 用户表 (users)
- id, username, email, password, nickname, avatar_url
- is_online, last_login_time, created_at, updated_at

### 聊天室表 (chat_rooms)
- id, name, description, creator_id, is_public
- max_members, current_members, room_avatar_url
- created_at, updated_at

### 消息表 (messages)
- id, chat_room_id, sender_id, content, message_type
- reply_to_message_id, is_edited, edited_at
- created_at, updated_at

### 用户聊天室关系表 (user_chat_rooms)
- id, user_id, chat_room_id, role
- is_muted, muted_until, last_read_message_id
- joined_at, updated_at

## 开发说明

### 后端开发

1. 使用MyBatis Plus进行数据库操作
2. JWT用于用户认证和授权
3. WebSocket处理实时通信
4. Spring Security配置安全策略

### 前端开发

1. 使用Provider进行状态管理
2. HTTP服务处理API调用
3. WebSocket服务处理实时通信
4. Material Design UI组件

## 部署说明

### 后端部署

1. 打包应用
   ```bash
   mvn clean package
   ```

2. 运行jar包
   ```bash
   java -jar target/chatroom-backend-1.0.0.jar
   ```

### 前端部署

1. 构建应用
   ```bash
   flutter build apk  # Android
   flutter build ios  # iOS
   flutter build web  # Web
   ```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请创建Issue或联系开发者。
