package com.chatroom.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chatroom.entity.ChatRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface ChatRoomMapper extends BaseMapper<ChatRoom> {

    @Select("SELECT * FROM chat_rooms WHERE is_public = true AND deleted = 0 ORDER BY created_at DESC")
    List<ChatRoom> findPublicChatRooms();

    @Select("SELECT cr.* FROM chat_rooms cr " +
            "INNER JOIN user_chat_rooms ucr ON cr.id = ucr.chat_room_id " +
            "WHERE ucr.user_id = #{userId} AND cr.deleted = 0 AND ucr.deleted = 0 " +
            "ORDER BY ucr.updated_at DESC")
    List<ChatRoom> findUserChatRooms(Long userId);

    @Update("UPDATE chat_rooms SET current_members = current_members + 1 WHERE id = #{chatRoomId}")
    int incrementMemberCount(Long chatRoomId);

    @Update("UPDATE chat_rooms SET current_members = current_members - 1 WHERE id = #{chatRoomId} AND current_members > 0")
    int decrementMemberCount(Long chatRoomId);

    @Select("SELECT COUNT(*) FROM user_chat_rooms WHERE chat_room_id = #{chatRoomId} AND deleted = 0")
    int getMemberCount(Long chatRoomId);
}
