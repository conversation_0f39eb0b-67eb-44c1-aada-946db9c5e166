# 项目启动问题排查

## 当前问题

项目遇到MyBatis Plus与Spring Boot 3.2.0兼容性问题：
```
java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
```

## 解决方案

### 方案1：降级Spring Boot版本（推荐）

修改 `backend/pom.xml`：
```xml
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.18</version>
    <relativePath/>
</parent>
```

同时调整依赖版本：
```xml
<properties>
    <java.version>17</java.version>
    <mybatis-plus.version>3.5.4</mybatis-plus.version>
    <jwt.version>0.11.5</jwt.version>
</properties>
```

### 方案2：使用标准MyBatis

1. 修改pom.xml，移除MyBatis Plus，使用标准MyBatis
2. 移除实体类中的MyBatis Plus注解
3. 创建标准的MyBatis XML映射文件

### 方案3：等待兼容版本

等待MyBatis Plus发布与Spring Boot 3.2.0完全兼容的版本。

## 临时解决方案

如果需要快速启动项目进行测试，可以：

1. 暂时禁用数据库相关功能
2. 使用内存数据库H2进行开发测试
3. 手动配置数据源和MyBatis

## 建议

**推荐使用方案1**，降级到Spring Boot 2.7.18，这是一个稳定的LTS版本，与MyBatis Plus 3.5.4完全兼容。

## 快速修复命令

```bash
# 1. 修改pom.xml中的Spring Boot版本为2.7.18
# 2. 清理并重新编译
mvn clean compile

# 3. 启动应用
mvn spring-boot:run
```

## 数据库准备

确保MariaDB已启动，并创建数据库：
```sql
CREATE DATABASE new_time CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

运行初始化脚本：
```bash
mysql -u root -p new_time < backend/src/main/resources/db/schema.sql
```
