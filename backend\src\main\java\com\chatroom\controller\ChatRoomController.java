package com.chatroom.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.chatroom.dto.ApiResponse;
import com.chatroom.dto.ChatRoomCreateRequest;
import com.chatroom.entity.ChatRoom;
import com.chatroom.entity.UserChatRoom;
import com.chatroom.service.ChatRoomService;
import com.chatroom.util.JwtUtil;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/chatrooms")
@CrossOrigin(origins = "*")
public class ChatRoomController {

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取公共聊天室列表
     */
    @GetMapping("/public")
    public ResponseEntity<ApiResponse<List<ChatRoom>>> getPublicChatRooms() {
        try {
            List<ChatRoom> chatRooms = chatRoomService.getPublicChatRooms();
            return ResponseEntity.ok(ApiResponse.success(chatRooms));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 获取用户加入的聊天室列表
     */
    @GetMapping("/my")
    public ResponseEntity<ApiResponse<List<ChatRoom>>> getUserChatRooms(@RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            List<ChatRoom> chatRooms = chatRoomService.getUserChatRooms(userId);
            return ResponseEntity.ok(ApiResponse.success(chatRooms));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 创建聊天室
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ChatRoom>> createChatRoom(
            @Valid @RequestBody ChatRoomCreateRequest request,
            @RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            ChatRoom chatRoom = chatRoomService.createChatRoom(request, userId);
            return ResponseEntity.ok(ApiResponse.success("聊天室创建成功", chatRoom));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 加入聊天室
     */
    @PostMapping("/{chatRoomId}/join")
    public ResponseEntity<ApiResponse<UserChatRoom>> joinChatRoom(
            @PathVariable Long chatRoomId,
            @RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            UserChatRoom userChatRoom = chatRoomService.joinChatRoom(chatRoomId, userId);
            return ResponseEntity.ok(ApiResponse.success("加入聊天室成功", userChatRoom));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 离开聊天室
     */
    @PostMapping("/{chatRoomId}/leave")
    public ResponseEntity<ApiResponse<String>> leaveChatRoom(
            @PathVariable Long chatRoomId,
            @RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            chatRoomService.leaveChatRoom(chatRoomId, userId);
            return ResponseEntity.ok(ApiResponse.success("离开聊天室成功", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 获取聊天室成员列表
     */
    @GetMapping("/{chatRoomId}/members")
    public ResponseEntity<ApiResponse<List<UserChatRoom>>> getChatRoomMembers(@PathVariable Long chatRoomId) {
        try {
            List<UserChatRoom> members = chatRoomService.getChatRoomMembers(chatRoomId);
            return ResponseEntity.ok(ApiResponse.success(members));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 获取聊天室在线成员列表
     */
    @GetMapping("/{chatRoomId}/online-members")
    public ResponseEntity<ApiResponse<List<UserChatRoom>>> getOnlineMembers(@PathVariable Long chatRoomId) {
        try {
            List<UserChatRoom> onlineMembers = chatRoomService.getOnlineMembers(chatRoomId);
            return ResponseEntity.ok(ApiResponse.success(onlineMembers));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 获取聊天室详情
     */
    @GetMapping("/{chatRoomId}")
    public ResponseEntity<ApiResponse<ChatRoom>> getChatRoomDetails(@PathVariable Long chatRoomId) {
        try {
            ChatRoom chatRoom = chatRoomService.getById(chatRoomId);
            if (chatRoom == null) {
                return ResponseEntity.badRequest().body(ApiResponse.notFound("聊天室不存在"));
            }
            return ResponseEntity.ok(ApiResponse.success(chatRoom));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 更新聊天室信息
     */
    @PutMapping("/{chatRoomId}")
    public ResponseEntity<ApiResponse<ChatRoom>> updateChatRoom(
            @PathVariable Long chatRoomId,
            @Valid @RequestBody ChatRoomCreateRequest request,
            @RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            ChatRoom chatRoom = chatRoomService.updateChatRoom(chatRoomId, request, userId);
            return ResponseEntity.ok(ApiResponse.success("聊天室更新成功", chatRoom));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 删除聊天室
     */
    @DeleteMapping("/{chatRoomId}")
    public ResponseEntity<ApiResponse<String>> deleteChatRoom(
            @PathVariable Long chatRoomId,
            @RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            chatRoomService.deleteChatRoom(chatRoomId, userId);
            return ResponseEntity.ok(ApiResponse.success("聊天室删除成功", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }
}
