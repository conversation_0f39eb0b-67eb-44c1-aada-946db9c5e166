package com.chatroom.service;

import com.chatroom.dto.WebSocketMessage;
import com.chatroom.entity.Message;
import com.chatroom.entity.User;
import com.chatroom.handler.ChatWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WebSocketMessageService {

    @Autowired
    private ChatWebSocketHandler chatWebSocketHandler;

    /**
     * 广播新消息到聊天室
     */
    public void broadcastNewMessage(Message message, User sender) {
        WebSocketMessage wsMessage = WebSocketMessage.createMessageEvent(
            message.getChatRoomId(),
            sender.getId(),
            sender.getUsername(),
            sender.getNickname(),
            sender.getAvatarUrl(),
            message.getContent(),
            message.getId()
        );
        
        chatWebSocketHandler.broadcastToChatRoom(message.getChatRoomId(), wsMessage);
    }

    /**
     * 广播用户加入聊天室事件
     */
    public void broadcastUserJoin(Long chatRoomId, User user) {
        WebSocketMessage wsMessage = WebSocketMessage.createUserJoinEvent(
            chatRoomId, user.getId(), user.getUsername(), user.getNickname());
        
        chatWebSocketHandler.broadcastToChatRoom(chatRoomId, wsMessage, user.getId());
    }

    /**
     * 广播用户离开聊天室事件
     */
    public void broadcastUserLeave(Long chatRoomId, User user) {
        WebSocketMessage wsMessage = WebSocketMessage.createUserLeaveEvent(
            chatRoomId, user.getId(), user.getUsername(), user.getNickname());
        
        chatWebSocketHandler.broadcastToChatRoom(chatRoomId, wsMessage, user.getId());
    }

    /**
     * 广播在线用户列表更新
     */
    public void broadcastOnlineUsers(Long chatRoomId, Object onlineUsers) {
        WebSocketMessage wsMessage = WebSocketMessage.createOnlineUsersEvent(chatRoomId, onlineUsers);
        
        chatWebSocketHandler.broadcastToChatRoom(chatRoomId, wsMessage);
    }
}
