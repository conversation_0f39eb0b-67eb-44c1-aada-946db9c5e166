-- 创建数据库
CREATE DATABASE IF NOT EXISTS chatroom_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE chatroom_db;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(20) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    is_online BOOLEAN DEFAULT FALSE COMMENT '是否在线',
    last_login_time DATETIME COMMENT '最后登录时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标记'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 聊天室表
CREATE TABLE IF NOT EXISTS chat_rooms (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '聊天室名称',
    description TEXT COMMENT '聊天室描述',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    max_members INT DEFAULT 100 COMMENT '最大成员数',
    current_members INT DEFAULT 0 COMMENT '当前成员数',
    room_avatar_url VARCHAR(500) COMMENT '聊天室头像URL',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标记',
    FOREIGN KEY (creator_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天室表';

-- 消息表
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chat_room_id BIGINT NOT NULL COMMENT '聊天室ID',
    sender_id BIGINT NOT NULL COMMENT '发送者ID',
    content TEXT NOT NULL COMMENT '消息内容',
    message_type VARCHAR(20) DEFAULT 'TEXT' COMMENT '消息类型',
    reply_to_message_id BIGINT COMMENT '回复的消息ID',
    is_edited BOOLEAN DEFAULT FALSE COMMENT '是否已编辑',
    edited_at DATETIME COMMENT '编辑时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标记',
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id),
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (reply_to_message_id) REFERENCES messages(id),
    INDEX idx_chat_room_created (chat_room_id, created_at),
    INDEX idx_sender_created (sender_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 用户聊天室关系表
CREATE TABLE IF NOT EXISTS user_chat_rooms (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    chat_room_id BIGINT NOT NULL COMMENT '聊天室ID',
    role VARCHAR(20) DEFAULT 'MEMBER' COMMENT '角色',
    is_muted BOOLEAN DEFAULT FALSE COMMENT '是否被禁言',
    muted_until DATETIME COMMENT '禁言到期时间',
    last_read_message_id BIGINT COMMENT '最后阅读的消息ID',
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标记',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id),
    FOREIGN KEY (last_read_message_id) REFERENCES messages(id),
    UNIQUE KEY uk_user_room (user_id, chat_room_id),
    INDEX idx_user_joined (user_id, joined_at),
    INDEX idx_room_joined (chat_room_id, joined_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户聊天室关系表';

-- 插入默认管理员用户
INSERT IGNORE INTO users (username, email, password, nickname, is_online) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '管理员', FALSE);

-- 获取管理员用户ID（假设为1，实际应该查询获取）
SET @admin_id = 1;

-- 插入默认聊天室数据
INSERT IGNORE INTO chat_rooms (name, description, creator_id, is_public, max_members) VALUES
('公共聊天室', '欢迎大家来到公共聊天室，请文明聊天！', @admin_id, TRUE, 1000),
('技术讨论', '技术相关话题讨论区', @admin_id, TRUE, 500),
('随便聊聊', '轻松愉快的聊天环境', @admin_id, TRUE, 200);

-- 管理员自动加入所有聊天室
INSERT IGNORE INTO user_chat_rooms (user_id, chat_room_id, role)
SELECT @admin_id, id, 'CREATOR' FROM chat_rooms WHERE creator_id = @admin_id;
