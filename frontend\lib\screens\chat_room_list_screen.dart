import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/chat_provider.dart';
import '../models/chat_room.dart';
import '../services/api_service.dart';
import 'chat_screen.dart';

class ChatRoomListScreen extends StatefulWidget {
  const ChatRoomListScreen({super.key});

  @override
  State<ChatRoomListScreen> createState() => _ChatRoomListScreenState();
}

class _ChatRoomListScreenState extends State<ChatRoomListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    await Future.wait([
      chatProvider.loadPublicChatRooms(),
      chatProvider.loadUserChatRooms(),
    ]);
  }

  Future<void> _logout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  Future<void> _joinChatRoom(ChatRoom chatRoom) async {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final success = await chatProvider.joinChatRoom(chatRoom);
    
    if (success) {
      _enterChatRoom(chatRoom);
    } else if (chatProvider.error != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(chatProvider.error!)),
      );
    }
  }

  void _enterChatRoom(ChatRoom chatRoom) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final token = await ApiService.getToken();
    
    if (token != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ChatScreen(chatRoom: chatRoom, token: token),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chat Rooms'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'logout') {
                _logout();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    const Icon(Icons.logout),
                    const SizedBox(width: 8),
                    Text('Logout (${authProvider.user?.displayName ?? ''})'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Public Rooms'),
            Tab(text: 'My Rooms'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPublicRoomsTab(),
          _buildMyRoomsTab(),
        ],
      ),
    );
  }

  Widget _buildPublicRoomsTab() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        if (chatProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (chatProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(chatProvider.error!),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (chatProvider.publicChatRooms.isEmpty) {
          return const Center(
            child: Text('No public chat rooms available'),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadData,
          child: ListView.builder(
            itemCount: chatProvider.publicChatRooms.length,
            itemBuilder: (context, index) {
              final chatRoom = chatProvider.publicChatRooms[index];
              return _buildChatRoomCard(chatRoom, isPublic: true);
            },
          ),
        );
      },
    );
  }

  Widget _buildMyRoomsTab() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        if (chatProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (chatProvider.userChatRooms.isEmpty) {
          return const Center(
            child: Text('You haven\'t joined any chat rooms yet'),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadData,
          child: ListView.builder(
            itemCount: chatProvider.userChatRooms.length,
            itemBuilder: (context, index) {
              final chatRoom = chatProvider.userChatRooms[index];
              return _buildChatRoomCard(chatRoom, isPublic: false);
            },
          ),
        );
      },
    );
  }

  Widget _buildChatRoomCard(ChatRoom chatRoom, {required bool isPublic}) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor,
          child: Text(
            chatRoom.name.substring(0, 1).toUpperCase(),
            style: const TextStyle(color: Colors.white),
          ),
        ),
        title: Text(chatRoom.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (chatRoom.description != null)
              Text(
                chatRoom.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 4),
            Text(
              '${chatRoom.currentMembers}/${chatRoom.maxMembers} members',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        trailing: isPublic
            ? IconButton(
                icon: const Icon(Icons.add),
                onPressed: () => _joinChatRoom(chatRoom),
              )
            : const Icon(Icons.arrow_forward_ios),
        onTap: isPublic ? null : () => _enterChatRoom(chatRoom),
      ),
    );
  }
}
