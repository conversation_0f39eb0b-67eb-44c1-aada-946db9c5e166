package com.chatroom.handler;

import java.io.IOException;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;

import com.chatroom.dto.WebSocketMessage;
import com.chatroom.entity.User;
import com.chatroom.service.ChatRoomService;
import com.chatroom.service.UserService;
import com.chatroom.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class ChatWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(ChatWebSocketHandler.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private ChatRoomService chatRoomService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 存储用户会话信息
    private final Map<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    // 存储聊天室用户映射
    private final Map<Long, Map<Long, WebSocketSession>> chatRoomUsers = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        logger.info("WebSocket connection established: {}", session.getId());
        
        // 从URL参数中获取token和chatRoomId
        URI uri = session.getUri();
        String query = uri.getQuery();
        
        if (query != null) {
            Map<String, String> params = parseQueryString(query);
            String token = params.get("token");
            String chatRoomIdStr = params.get("chatRoomId");
            
            if (token != null && chatRoomIdStr != null) {
                try {
                    Long chatRoomId = Long.parseLong(chatRoomIdStr);
                    
                    // 验证token
                    if (jwtUtil.validateTokenFormat(token)) {
                        String username = jwtUtil.getUsernameFromToken(token);
                        Long userId = jwtUtil.getUserIdFromToken(token);
                        
                        User user = userService.findByUsername(username);
                        if (user != null && user.getId().equals(userId)) {
                            // 检查用户是否在聊天室中
                            if (chatRoomService.isUserInChatRoom(userId, chatRoomId)) {
                                // 保存会话信息
                                session.getAttributes().put("userId", userId);
                                session.getAttributes().put("username", username);
                                session.getAttributes().put("chatRoomId", chatRoomId);
                                session.getAttributes().put("user", user);
                                
                                userSessions.put(session.getId(), session);
                                
                                // 添加到聊天室用户映射
                                chatRoomUsers.computeIfAbsent(chatRoomId, k -> new ConcurrentHashMap<>())
                                            .put(userId, session);
                                
                                // 更新用户在线状态
                                userService.updateOnlineStatus(userId, true);
                                
                                // 通知其他用户有新用户加入
                                broadcastUserJoin(chatRoomId, user);
                                
                                logger.info("User {} joined chat room {}", username, chatRoomId);
                            } else {
                                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("User not in chat room"));
                            }
                        } else {
                            session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Invalid user"));
                        }
                    } else {
                        session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Invalid token"));
                    }
                } catch (NumberFormatException e) {
                    session.close(CloseStatus.BAD_DATA.withReason("Invalid chatRoomId"));
                }
            } else {
                session.close(CloseStatus.BAD_DATA.withReason("Missing token or chatRoomId"));
            }
        } else {
            session.close(CloseStatus.BAD_DATA.withReason("Missing query parameters"));
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, org.springframework.web.socket.WebSocketMessage<?> message) throws Exception {
        if (message instanceof TextMessage) {
            String payload = ((TextMessage) message).getPayload();
            logger.info("Received message: {}", payload);

            try {
                WebSocketMessage wsMessage = objectMapper.readValue(payload, WebSocketMessage.class);
                handleWebSocketMessage(session, wsMessage);
            } catch (Exception e) {
                logger.error("Error parsing WebSocket message", e);
            }
        }
    }

    private void handleWebSocketMessage(WebSocketSession session, WebSocketMessage wsMessage) {
        Long userId = (Long) session.getAttributes().get("userId");
        Long chatRoomId = (Long) session.getAttributes().get("chatRoomId");
        User user = (User) session.getAttributes().get("user");
        
        if (userId == null || chatRoomId == null || user == null) {
            return;
        }
        
        switch (wsMessage.getType()) {
            case "TYPING":
                handleTypingEvent(chatRoomId, user, (Boolean) wsMessage.getData());
                break;
            case "PING":
                sendPong(session);
                break;
            default:
                logger.warn("Unknown message type: {}", wsMessage.getType());
        }
    }

    private void handleTypingEvent(Long chatRoomId, User user, Boolean isTyping) {
        WebSocketMessage typingMessage = WebSocketMessage.createTypingEvent(
            chatRoomId, user.getId(), user.getUsername(), isTyping != null ? isTyping : false);
        broadcastToChatRoom(chatRoomId, typingMessage, user.getId());
    }

    private void sendPong(WebSocketSession session) {
        try {
            WebSocketMessage pong = new WebSocketMessage("PONG");
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(pong)));
        } catch (Exception e) {
            logger.error("Error sending pong", e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket transport error for session {}: {}", session.getId(), exception.getMessage());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        logger.info("WebSocket connection closed: {} with status: {}", session.getId(), closeStatus);
        
        Long userId = (Long) session.getAttributes().get("userId");
        Long chatRoomId = (Long) session.getAttributes().get("chatRoomId");
        User user = (User) session.getAttributes().get("user");
        
        if (userId != null && chatRoomId != null && user != null) {
            // 从映射中移除
            userSessions.remove(session.getId());
            Map<Long, WebSocketSession> roomUsers = chatRoomUsers.get(chatRoomId);
            if (roomUsers != null) {
                roomUsers.remove(userId);
                if (roomUsers.isEmpty()) {
                    chatRoomUsers.remove(chatRoomId);
                }
            }
            
            // 检查用户是否还有其他活跃连接
            boolean hasOtherConnections = userSessions.values().stream()
                .anyMatch(s -> userId.equals(s.getAttributes().get("userId")));
            
            if (!hasOtherConnections) {
                // 更新用户离线状态
                userService.updateOnlineStatus(userId, false);
            }
            
            // 通知其他用户有用户离开
            broadcastUserLeave(chatRoomId, user);
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    // 广播消息到聊天室
    public void broadcastToChatRoom(Long chatRoomId, WebSocketMessage message) {
        broadcastToChatRoom(chatRoomId, message, null);
    }

    public void broadcastToChatRoom(Long chatRoomId, WebSocketMessage message, Long excludeUserId) {
        Map<Long, WebSocketSession> roomUsers = chatRoomUsers.get(chatRoomId);
        if (roomUsers != null) {
            String messageJson;
            try {
                messageJson = objectMapper.writeValueAsString(message);
            } catch (Exception e) {
                logger.error("Error serializing message", e);
                return;
            }
            
            roomUsers.entrySet().removeIf(entry -> {
                Long userId = entry.getKey();
                WebSocketSession session = entry.getValue();
                
                if (excludeUserId != null && excludeUserId.equals(userId)) {
                    return false; // 不发送给排除的用户
                }
                
                if (session.isOpen()) {
                    try {
                        session.sendMessage(new TextMessage(messageJson));
                        return false; // 保留会话
                    } catch (IOException e) {
                        logger.error("Error sending message to user {}", userId, e);
                        return true; // 移除无效会话
                    }
                } else {
                    return true; // 移除关闭的会话
                }
            });
        }
    }

    private void broadcastUserJoin(Long chatRoomId, User user) {
        WebSocketMessage joinMessage = WebSocketMessage.createUserJoinEvent(
            chatRoomId, user.getId(), user.getUsername(), user.getNickname());
        broadcastToChatRoom(chatRoomId, joinMessage, user.getId());
    }

    private void broadcastUserLeave(Long chatRoomId, User user) {
        WebSocketMessage leaveMessage = WebSocketMessage.createUserLeaveEvent(
            chatRoomId, user.getId(), user.getUsername(), user.getNickname());
        broadcastToChatRoom(chatRoomId, leaveMessage, user.getId());
    }

    private Map<String, String> parseQueryString(String query) {
        Map<String, String> params = new ConcurrentHashMap<>();
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2) {
                params.put(keyValue[0], keyValue[1]);
            }
        }
        return params;
    }
}
