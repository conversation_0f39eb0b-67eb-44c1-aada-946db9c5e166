package com.chatroom.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

public class WebSocketMessage {
    
    private String type; // MESSAGE, USER_JOIN, USER_LEAVE, TYPING, ONLINE_USERS
    private Long chatRoomId;
    private Long senderId;
    private String senderUsername;
    private String senderNickname;
    private String senderAvatarUrl;
    private String content;
    private Long messageId;
    private Object data;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    // Constructors
    public WebSocketMessage() {
        this.timestamp = LocalDateTime.now();
    }

    public WebSocketMessage(String type) {
        this.type = type;
        this.timestamp = LocalDateTime.now();
    }

    public WebSocketMessage(String type, Long chatRoomId) {
        this.type = type;
        this.chatRoomId = chatRoomId;
        this.timestamp = LocalDateTime.now();
    }

    // Static factory methods
    public static WebSocketMessage createMessageEvent(Long chatRoomId, Long senderId, String senderUsername, 
                                                     String senderNickname, String senderAvatarUrl, 
                                                     String content, Long messageId) {
        WebSocketMessage message = new WebSocketMessage("MESSAGE", chatRoomId);
        message.setSenderId(senderId);
        message.setSenderUsername(senderUsername);
        message.setSenderNickname(senderNickname);
        message.setSenderAvatarUrl(senderAvatarUrl);
        message.setContent(content);
        message.setMessageId(messageId);
        return message;
    }

    public static WebSocketMessage createUserJoinEvent(Long chatRoomId, Long userId, String username, String nickname) {
        WebSocketMessage message = new WebSocketMessage("USER_JOIN", chatRoomId);
        message.setSenderId(userId);
        message.setSenderUsername(username);
        message.setSenderNickname(nickname);
        return message;
    }

    public static WebSocketMessage createUserLeaveEvent(Long chatRoomId, Long userId, String username, String nickname) {
        WebSocketMessage message = new WebSocketMessage("USER_LEAVE", chatRoomId);
        message.setSenderId(userId);
        message.setSenderUsername(username);
        message.setSenderNickname(nickname);
        return message;
    }

    public static WebSocketMessage createTypingEvent(Long chatRoomId, Long userId, String username, boolean isTyping) {
        WebSocketMessage message = new WebSocketMessage("TYPING", chatRoomId);
        message.setSenderId(userId);
        message.setSenderUsername(username);
        message.setData(isTyping);
        return message;
    }

    public static WebSocketMessage createOnlineUsersEvent(Long chatRoomId, Object onlineUsers) {
        WebSocketMessage message = new WebSocketMessage("ONLINE_USERS", chatRoomId);
        message.setData(onlineUsers);
        return message;
    }

    // Getters and Setters
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getChatRoomId() {
        return chatRoomId;
    }

    public void setChatRoomId(Long chatRoomId) {
        this.chatRoomId = chatRoomId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String getSenderUsername() {
        return senderUsername;
    }

    public void setSenderUsername(String senderUsername) {
        this.senderUsername = senderUsername;
    }

    public String getSenderNickname() {
        return senderNickname;
    }

    public void setSenderNickname(String senderNickname) {
        this.senderNickname = senderNickname;
    }

    public String getSenderAvatarUrl() {
        return senderAvatarUrl;
    }

    public void setSenderAvatarUrl(String senderAvatarUrl) {
        this.senderAvatarUrl = senderAvatarUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getMessageId() {
        return messageId;
    }

    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}
