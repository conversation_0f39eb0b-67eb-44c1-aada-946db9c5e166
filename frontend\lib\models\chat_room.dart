import 'package:json_annotation/json_annotation.dart';

part 'chat_room.g.dart';

@JsonSerializable()
class ChatRoom {
  final int id;
  final String name;
  final String? description;
  final int creatorId;
  final bool isPublic;
  final int maxMembers;
  final int currentMembers;
  final String? roomAvatarUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  ChatRoom({
    required this.id,
    required this.name,
    this.description,
    required this.creatorId,
    required this.isPublic,
    required this.maxMembers,
    required this.currentMembers,
    this.roomAvatarUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ChatRoom.fromJson(Map<String, dynamic> json) => _$ChatRoomFromJson(json);
  Map<String, dynamic> toJson() => _$ChatRoomToJson(this);
}
