package com.chatroom.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chatroom.entity.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    @Select("SELECT m.*, u.username as senderUsername, u.nickname as senderNickname, u.avatar_url as senderAvatarUrl " +
            "FROM messages m " +
            "INNER JOIN users u ON m.sender_id = u.id " +
            "WHERE m.chat_room_id = #{chatRoomId} AND m.deleted = 0 " +
            "ORDER BY m.created_at DESC")
    IPage<Message> findMessagesByChatRoomId(Page<Message> page, @Param("chatRoomId") Long chatRoomId);

    @Select("SELECT m.*, u.username as senderUsername, u.nickname as senderNickname, u.avatar_url as senderAvatarUrl " +
            "FROM messages m " +
            "INNER JOIN users u ON m.sender_id = u.id " +
            "WHERE m.chat_room_id = #{chatRoomId} AND m.deleted = 0 " +
            "ORDER BY m.created_at DESC " +
            "LIMIT #{limit}")
    List<Message> findRecentMessages(@Param("chatRoomId") Long chatRoomId, @Param("limit") int limit);

    @Select("SELECT COUNT(*) FROM messages WHERE chat_room_id = #{chatRoomId} AND deleted = 0")
    int countMessagesByChatRoomId(Long chatRoomId);

    @Select("SELECT m.*, u.username as senderUsername, u.nickname as senderNickname, u.avatar_url as senderAvatarUrl " +
            "FROM messages m " +
            "INNER JOIN users u ON m.sender_id = u.id " +
            "WHERE m.chat_room_id = #{chatRoomId} AND m.id > #{lastMessageId} AND m.deleted = 0 " +
            "ORDER BY m.created_at ASC")
    List<Message> findNewMessages(@Param("chatRoomId") Long chatRoomId, @Param("lastMessageId") Long lastMessageId);
}
