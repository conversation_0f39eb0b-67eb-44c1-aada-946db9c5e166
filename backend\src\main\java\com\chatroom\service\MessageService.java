package com.chatroom.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chatroom.dto.MessageSendRequest;
import com.chatroom.entity.Message;

import java.util.List;

public interface MessageService extends IService<Message> {

    /**
     * 发送消息
     */
    Message sendMessage(MessageSendRequest request, Long senderId);

    /**
     * 获取聊天室消息（分页）
     */
    IPage<Message> getChatRoomMessages(Long chatRoomId, int page, int size);

    /**
     * 获取最近消息
     */
    List<Message> getRecentMessages(Long chatRoomId, int limit);

    /**
     * 获取新消息
     */
    List<Message> getNewMessages(Long chatRoomId, Long lastMessageId);

    /**
     * 删除消息
     */
    void deleteMessage(Long messageId, Long userId);

    /**
     * 编辑消息
     */
    Message editMessage(Long messageId, String newContent, Long userId);

    /**
     * 获取聊天室消息数量
     */
    int getMessageCount(Long chatRoomId);
}
