package com.chatroom.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chatroom.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface UserMapper extends BaseMapper<User> {

    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = 0")
    User findByUsername(String username);

    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(String email);

    @Update("UPDATE users SET is_online = #{isOnline}, last_login_time = #{lastLoginTime} WHERE id = #{userId}")
    int updateOnlineStatus(Long userId, Boolean isOnline, LocalDateTime lastLoginTime);

    @Select("SELECT * FROM users WHERE is_online = true AND deleted = 0")
    List<User> findOnlineUsers();

    @Select("SELECT COUNT(*) FROM users WHERE is_online = true AND deleted = 0")
    int countOnlineUsers();
}
