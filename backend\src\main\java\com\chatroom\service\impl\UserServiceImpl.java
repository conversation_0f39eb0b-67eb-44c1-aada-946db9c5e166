package com.chatroom.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chatroom.dto.AuthResponse;
import com.chatroom.dto.LoginRequest;
import com.chatroom.dto.RegisterRequest;
import com.chatroom.entity.User;
import com.chatroom.mapper.UserMapper;
import com.chatroom.service.UserService;
import com.chatroom.util.JwtUtil;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Value("${jwt.expiration}")
    private Long jwtExpiration;

    @Override
    public AuthResponse register(RegisterRequest request) {
        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("密码确认不匹配");
        }

        // 检查用户名是否已存在
        if (userMapper.findByUsername(request.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userMapper.findByEmail(request.getEmail()) != null) {
            throw new RuntimeException("邮箱已被注册");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setNickname(StringUtils.hasText(request.getNickname()) ? 
                        request.getNickname() : request.getUsername());
        user.setIsOnline(true);
        user.setLastLoginTime(LocalDateTime.now());

        // 保存用户
        save(user);

        // 生成token
        String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername());
        String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());

        return new AuthResponse(accessToken, refreshToken, jwtExpiration, user);
    }

    @Override
    public AuthResponse login(LoginRequest request) {
        // 查找用户
        User user = userMapper.findByUsername(request.getUsername());
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 更新登录状态
        user.setIsOnline(true);
        user.setLastLoginTime(LocalDateTime.now());
        updateById(user);

        // 生成token
        String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername());
        String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());

        return new AuthResponse(accessToken, refreshToken, jwtExpiration, user);
    }

    @Override
    public AuthResponse refreshToken(String refreshToken) {
        try {
            if (!jwtUtil.validateTokenFormat(refreshToken)) {
                throw new RuntimeException("无效的刷新token");
            }

            String tokenType = jwtUtil.getTokenType(refreshToken);
            if (!"refresh".equals(tokenType)) {
                throw new RuntimeException("token类型错误");
            }

            String username = jwtUtil.getUsernameFromToken(refreshToken);
            Long userId = jwtUtil.getUserIdFromToken(refreshToken);

            User user = userMapper.findByUsername(username);
            if (user == null || !user.getId().equals(userId)) {
                throw new RuntimeException("用户不存在");
            }

            // 生成新的token
            String newAccessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername());
            String newRefreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());

            return new AuthResponse(newAccessToken, newRefreshToken, jwtExpiration, user);
        } catch (Exception e) {
            throw new RuntimeException("刷新token失败: " + e.getMessage());
        }
    }

    @Override
    public void logout(Long userId) {
        User user = getById(userId);
        if (user != null) {
            user.setIsOnline(false);
            updateById(user);
        }
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    public void updateOnlineStatus(Long userId, Boolean isOnline) {
        userMapper.updateOnlineStatus(userId, isOnline, LocalDateTime.now());
    }

    @Override
    public List<User> getOnlineUsers() {
        return userMapper.findOnlineUsers();
    }

    @Override
    public int getOnlineUserCount() {
        return userMapper.countOnlineUsers();
    }

    @Override
    public boolean validatePassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
