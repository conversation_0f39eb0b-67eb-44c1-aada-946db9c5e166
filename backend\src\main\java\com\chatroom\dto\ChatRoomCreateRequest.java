package com.chatroom.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class ChatRoomCreateRequest {
    
    @NotBlank(message = "聊天室名称不能为空")
    @Size(min = 1, max = 50, message = "聊天室名称长度必须在1-50个字符之间")
    private String name;
    
    private String description;
    
    private Boolean isPublic = true;
    
    private Integer maxMembers = 100;
    
    private String roomAvatarUrl;

    // Constructors
    public ChatRoomCreateRequest() {}

    public ChatRoomCreateRequest(String name, String description) {
        this.name = name;
        this.description = description;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Integer getMaxMembers() {
        return maxMembers;
    }

    public void setMaxMembers(Integer maxMembers) {
        this.maxMembers = maxMembers;
    }

    public String getRoomAvatarUrl() {
        return roomAvatarUrl;
    }

    public void setRoomAvatarUrl(String roomAvatarUrl) {
        this.roomAvatarUrl = roomAvatarUrl;
    }
}
