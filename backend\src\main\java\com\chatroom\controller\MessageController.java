package com.chatroom.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chatroom.dto.ApiResponse;
import com.chatroom.dto.MessageSendRequest;
import com.chatroom.entity.Message;
import com.chatroom.entity.User;
import com.chatroom.service.MessageService;
import com.chatroom.service.UserService;
import com.chatroom.service.WebSocketMessageService;
import com.chatroom.util.JwtUtil;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/messages")
@CrossOrigin(origins = "*")
public class MessageController {

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserService userService;

    @Autowired
    private WebSocketMessageService webSocketMessageService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 发送消息
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Message>> sendMessage(
            @Valid @RequestBody MessageSendRequest request,
            @RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            Message message = messageService.sendMessage(request, userId);

            // 获取发送者信息并通过WebSocket广播消息
            User sender = userService.getById(userId);
            if (sender != null) {
                webSocketMessageService.broadcastNewMessage(message, sender);
            }

            return ResponseEntity.ok(ApiResponse.success("消息发送成功", message));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 获取聊天室消息（分页）
     */
    @GetMapping("/chatroom/{chatRoomId}")
    public ResponseEntity<ApiResponse<IPage<Message>>> getChatRoomMessages(
            @PathVariable Long chatRoomId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            IPage<Message> messages = messageService.getChatRoomMessages(chatRoomId, page, size);
            return ResponseEntity.ok(ApiResponse.success(messages));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 获取最近消息
     */
    @GetMapping("/chatroom/{chatRoomId}/recent")
    public ResponseEntity<ApiResponse<List<Message>>> getRecentMessages(
            @PathVariable Long chatRoomId,
            @RequestParam(defaultValue = "50") int limit) {
        try {
            List<Message> messages = messageService.getRecentMessages(chatRoomId, limit);
            return ResponseEntity.ok(ApiResponse.success(messages));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 获取新消息
     */
    @GetMapping("/chatroom/{chatRoomId}/new")
    public ResponseEntity<ApiResponse<List<Message>>> getNewMessages(
            @PathVariable Long chatRoomId,
            @RequestParam Long lastMessageId) {
        try {
            List<Message> messages = messageService.getNewMessages(chatRoomId, lastMessageId);
            return ResponseEntity.ok(ApiResponse.success(messages));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    public ResponseEntity<ApiResponse<String>> deleteMessage(
            @PathVariable Long messageId,
            @RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            messageService.deleteMessage(messageId, userId);
            return ResponseEntity.ok(ApiResponse.success("消息删除成功", null));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    /**
     * 编辑消息
     */
    @PutMapping("/{messageId}")
    public ResponseEntity<ApiResponse<Message>> editMessage(
            @PathVariable Long messageId,
            @RequestBody EditMessageRequest request,
            @RequestHeader("Authorization") String token) {
        try {
            String jwt = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(jwt);
            Message message = messageService.editMessage(messageId, request.getContent(), userId);
            return ResponseEntity.ok(ApiResponse.success("消息编辑成功", message));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        }
    }

    // EditMessageRequest DTO
    public static class EditMessageRequest {
        private String content;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
