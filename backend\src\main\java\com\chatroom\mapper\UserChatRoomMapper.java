package com.chatroom.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chatroom.entity.UserChatRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserChatRoomMapper extends BaseMapper<UserChatRoom> {

    @Select("SELECT * FROM user_chat_rooms WHERE user_id = #{userId} AND chat_room_id = #{chatRoomId} AND deleted = 0")
    UserChatRoom findByUserIdAndChatRoomId(@Param("userId") Long userId, @Param("chatRoomId") Long chatRoomId);

    @Select("SELECT ucr.*, u.username, u.nickname, u.avatar_url, u.is_online " +
            "FROM user_chat_rooms ucr " +
            "INNER JOIN users u ON ucr.user_id = u.id " +
            "WHERE ucr.chat_room_id = #{chatRoomId} AND ucr.deleted = 0 AND u.deleted = 0 " +
            "ORDER BY ucr.joined_at ASC")
    List<UserChatRoom> findMembersByChatRoomId(Long chatRoomId);

    @Select("SELECT COUNT(*) FROM user_chat_rooms WHERE chat_room_id = #{chatRoomId} AND deleted = 0")
    int countMembersByChatRoomId(Long chatRoomId);

    @Select("SELECT ucr.*, u.username, u.nickname, u.avatar_url, u.is_online " +
            "FROM user_chat_rooms ucr " +
            "INNER JOIN users u ON ucr.user_id = u.id " +
            "WHERE ucr.chat_room_id = #{chatRoomId} AND u.is_online = true AND ucr.deleted = 0 AND u.deleted = 0")
    List<UserChatRoom> findOnlineMembersByChatRoomId(Long chatRoomId);

    @Select("SELECT * FROM user_chat_rooms WHERE user_id = #{userId} AND deleted = 0")
    List<UserChatRoom> findByUserId(Long userId);
}
