import 'package:json_annotation/json_annotation.dart';

part 'message.g.dart';

@JsonSerializable()
class Message {
  final int id;
  final int chatRoomId;
  final int senderId;
  final String content;
  final String messageType;
  final int? replyToMessageId;
  final bool isEdited;
  final DateTime? editedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Additional fields from join query
  final String? senderUsername;
  final String? senderNickname;
  final String? senderAvatarUrl;

  Message({
    required this.id,
    required this.chatRoomId,
    required this.senderId,
    required this.content,
    required this.messageType,
    this.replyToMessageId,
    required this.isEdited,
    this.editedAt,
    required this.createdAt,
    required this.updatedAt,
    this.senderUsername,
    this.senderNickname,
    this.senderAvatarUrl,
  });

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);
  Map<String, dynamic> toJson() => _$MessageToJson(this);

  String get senderDisplayName => senderNickname ?? senderUsername ?? 'Unknown';
}
