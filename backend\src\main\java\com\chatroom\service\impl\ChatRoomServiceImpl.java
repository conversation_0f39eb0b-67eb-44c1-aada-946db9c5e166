package com.chatroom.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chatroom.dto.ChatRoomCreateRequest;
import com.chatroom.entity.ChatRoom;
import com.chatroom.entity.UserChatRoom;
import com.chatroom.mapper.ChatRoomMapper;
import com.chatroom.mapper.UserChatRoomMapper;
import com.chatroom.service.ChatRoomService;

@Service
public class ChatRoomServiceImpl extends ServiceImpl<ChatRoomMapper, ChatRoom> implements ChatRoomService {

    @Autowired
    private ChatRoomMapper chatRoomMapper;

    @Autowired
    private UserChatRoomMapper userChatRoomMapper;

    @Override
    @Transactional
    public ChatRoom createChatRoom(ChatRoomCreateRequest request, Long creatorId) {
        ChatRoom chatRoom = new ChatRoom();
        chatRoom.setName(request.getName());
        chatRoom.setDescription(request.getDescription());
        chatRoom.setCreatorId(creatorId);
        chatRoom.setIsPublic(request.getIsPublic());
        chatRoom.setMaxMembers(request.getMaxMembers());
        chatRoom.setRoomAvatarUrl(request.getRoomAvatarUrl());
        chatRoom.setCurrentMembers(1);

        save(chatRoom);

        // 创建者自动加入聊天室
        UserChatRoom userChatRoom = new UserChatRoom();
        userChatRoom.setUserId(creatorId);
        userChatRoom.setChatRoomId(chatRoom.getId());
        userChatRoom.setRole("CREATOR");
        userChatRoomMapper.insert(userChatRoom);

        return chatRoom;
    }

    @Override
    @Transactional
    public UserChatRoom joinChatRoom(Long chatRoomId, Long userId) {
        // 检查聊天室是否存在
        ChatRoom chatRoom = getById(chatRoomId);
        if (chatRoom == null) {
            throw new RuntimeException("聊天室不存在");
        }

        // 检查用户是否已在聊天室中
        UserChatRoom existing = userChatRoomMapper.findByUserIdAndChatRoomId(userId, chatRoomId);
        if (existing != null) {
            throw new RuntimeException("您已在该聊天室中");
        }

        // 检查聊天室是否已满
        int currentMembers = userChatRoomMapper.countMembersByChatRoomId(chatRoomId);
        if (currentMembers >= chatRoom.getMaxMembers()) {
            throw new RuntimeException("聊天室已满");
        }

        // 加入聊天室
        UserChatRoom userChatRoom = new UserChatRoom();
        userChatRoom.setUserId(userId);
        userChatRoom.setChatRoomId(chatRoomId);
        userChatRoom.setRole("MEMBER");
        userChatRoomMapper.insert(userChatRoom);

        // 更新成员数量
        chatRoomMapper.incrementMemberCount(chatRoomId);

        return userChatRoom;
    }

    @Override
    @Transactional
    public void leaveChatRoom(Long chatRoomId, Long userId) {
        UserChatRoom userChatRoom = userChatRoomMapper.findByUserIdAndChatRoomId(userId, chatRoomId);
        if (userChatRoom == null) {
            throw new RuntimeException("您不在该聊天室中");
        }

        // 如果是创建者，不能离开聊天室
        if ("CREATOR".equals(userChatRoom.getRole())) {
            throw new RuntimeException("创建者不能离开聊天室");
        }

        // 删除用户聊天室关系
        userChatRoomMapper.deleteById(userChatRoom.getId());

        // 更新成员数量
        chatRoomMapper.decrementMemberCount(chatRoomId);
    }

    @Override
    public List<ChatRoom> getPublicChatRooms() {
        return chatRoomMapper.findPublicChatRooms();
    }

    @Override
    public List<ChatRoom> getUserChatRooms(Long userId) {
        return chatRoomMapper.findUserChatRooms(userId);
    }

    @Override
    public List<UserChatRoom> getChatRoomMembers(Long chatRoomId) {
        return userChatRoomMapper.findMembersByChatRoomId(chatRoomId);
    }

    @Override
    public List<UserChatRoom> getOnlineMembers(Long chatRoomId) {
        return userChatRoomMapper.findOnlineMembersByChatRoomId(chatRoomId);
    }

    @Override
    public boolean isUserInChatRoom(Long userId, Long chatRoomId) {
        UserChatRoom userChatRoom = userChatRoomMapper.findByUserIdAndChatRoomId(userId, chatRoomId);
        return userChatRoom != null;
    }

    @Override
    public int getMemberCount(Long chatRoomId) {
        return userChatRoomMapper.countMembersByChatRoomId(chatRoomId);
    }

    @Override
    @Transactional
    public ChatRoom updateChatRoom(Long chatRoomId, ChatRoomCreateRequest request, Long userId) {
        ChatRoom chatRoom = getById(chatRoomId);
        if (chatRoom == null) {
            throw new RuntimeException("聊天室不存在");
        }

        // 检查权限（只有创建者可以修改）
        UserChatRoom userChatRoom = userChatRoomMapper.findByUserIdAndChatRoomId(userId, chatRoomId);
        if (userChatRoom == null || !"CREATOR".equals(userChatRoom.getRole())) {
            throw new RuntimeException("没有权限修改聊天室");
        }

        chatRoom.setName(request.getName());
        chatRoom.setDescription(request.getDescription());
        chatRoom.setIsPublic(request.getIsPublic());
        chatRoom.setMaxMembers(request.getMaxMembers());
        chatRoom.setRoomAvatarUrl(request.getRoomAvatarUrl());

        updateById(chatRoom);
        return chatRoom;
    }

    @Override
    @Transactional
    public void deleteChatRoom(Long chatRoomId, Long userId) {
        ChatRoom chatRoom = getById(chatRoomId);
        if (chatRoom == null) {
            throw new RuntimeException("聊天室不存在");
        }

        // 检查权限（只有创建者可以删除）
        UserChatRoom userChatRoom = userChatRoomMapper.findByUserIdAndChatRoomId(userId, chatRoomId);
        if (userChatRoom == null || !"CREATOR".equals(userChatRoom.getRole())) {
            throw new RuntimeException("没有权限删除聊天室");
        }

        // 删除聊天室（逻辑删除）
        removeById(chatRoomId);
    }
}
